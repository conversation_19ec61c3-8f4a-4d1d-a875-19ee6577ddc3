# FraudPy Comprehensive Test Suite

This directory contains comprehensive end-to-end tests for the FraudPy fraud detection system. The tests validate the complete business logic and use cases of the fraud system.

## Overview

The comprehensive test suite covers:

### 🔄 Transaction Processing Pipeline
- Transaction submission through the mock producer
- Processing through router, statistics calculator, evaluator, and result writer
- End-to-end transaction flow validation

### 🛡️ Fraud Detection Rules
- **Blacklist Testing**: Validates that blacklisted accounts are properly flagged
- **Watchlist Testing**: Ensures watchlisted accounts are marked for manual review
- **Stafflist Testing**: Confirms staff accounts are exempt from fraud rules
- **Limits Enforcement**: Tests single transaction and velocity limits
- **Card Velocity**: Validates impossible travel detection (same card, different countries within 6 hours)

### 📋 Case Management Workflow
- Case creation for suspicious transactions
- Case status updates and transitions
- Report attachment to cases
- Case closure and archival
- Case listing and retrieval

### 📊 Dashboard Analytics
- Transaction summary reporting
- Evaluated transactions queries with various filters
- Case management reporting
- Real-time fraud statistics

### ⚙️ Administrative Functions
- Lists management (CRUD operations for blacklist, watchlist, stafflist)
- Limits management (CRUD operations for transaction limits)
- Configuration validation

## Business Use Cases Tested

### Scenario 1: Normal Transaction Flow
1. Submit a legitimate transaction from a clean account
2. Verify it processes without triggering any fraud rules
3. Confirm it appears in dashboard analytics as "normal"

### Scenario 2: Fraud Detection and Investigation
1. Submit transaction from blacklisted account
2. Verify it's flagged for fraud
3. Create investigation case
4. Update case status through workflow
5. Attach investigation reports
6. Close case when resolved

### Scenario 3: Risk Management
1. Submit transactions that exceed configured limits
2. Verify limit enforcement triggers
3. Test velocity limits with multiple transactions
4. Validate impossible travel detection

### Scenario 4: Operational Management
1. Add/remove accounts from various lists
2. Configure and update transaction limits
3. Query transaction data with various filters
4. Generate fraud statistics and reports

## Setup and Usage

### Prerequisites
1. Ensure the FraudPy API is deployed and accessible
2. Create a `.env` file in the project root with your API endpoint:
   ```
   BASE_URL=https://your-api-id.execute-api.region.amazonaws.com/Prod/
   ```

### Installation
```bash
# Install test dependencies
pip install -r tests/requirements.txt
```

### Running the Tests

#### Comprehensive Test Suite
```bash
# Run the complete end-to-end test suite
python tests/comprehensive_fraud_system_test.py
```

This will:
- Set up test data (accounts, lists, limits)
- Execute all business use case tests
- Generate a comprehensive test report
- Save detailed results to a JSON file

#### Individual Component Tests
You can also run individual component tests from the `scripts/` directory:
```bash
python scripts/case_management_test.py
python scripts/limits_test.py
python scripts/lists_test.py
python scripts/evaluated_transactions_test.py
python scripts/transaction_summary_test.py
```

## Test Output

The comprehensive test suite provides:

### Console Output
- Real-time test execution status
- Pass/fail indicators for each test
- Detailed error messages for failures
- Summary statistics and success rate

### Test Report File
- JSON file with complete test results
- Timestamp and execution duration
- Detailed failure analysis
- Test data used during execution

### Example Output
```
🚀 Starting Comprehensive FraudPy System Tests
🎯 Target API: https://your-api.execute-api.region.amazonaws.com/Prod/

🔧 Setting up test data...
✅ Test data setup complete

🧪 Testing normal transaction processing...
✅ PASS: Normal Transaction Submission
✅ PASS: Normal Transaction Processing

🧪 Testing blacklist transaction blocking...
✅ PASS: Blacklist Transaction Submission
✅ PASS: Blacklist Detection

📊 COMPREHENSIVE TEST REPORT
================================================================================
📈 Test Summary:
   Total Tests: 25
   ✅ Passed: 24
   ❌ Failed: 1
   ⏱️  Duration: 45.32 seconds
   📊 Success Rate: 96.0%
```

## Test Data Management

The test suite automatically:
- Creates unique test accounts for each test run
- Sets up required lists and limits
- Cleans up test data where possible
- Uses deterministic test data for reproducible results

## Troubleshooting

### Common Issues

1. **BASE_URL not configured**
   - Ensure `.env` file exists in project root
   - Verify API endpoint is correct and accessible

2. **API timeouts**
   - Check if the FraudPy services are running
   - Verify network connectivity to AWS services

3. **Permission errors**
   - Ensure API has proper IAM permissions
   - Check DynamoDB and SQS access rights

4. **Test data conflicts**
   - Tests use unique IDs to avoid conflicts
   - If issues persist, check DynamoDB for orphaned test data

### Debug Mode
For detailed debugging, modify the test script to enable verbose logging:
```python
# Add at the top of comprehensive_fraud_system_test.py
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Contributing

When adding new tests:
1. Follow the existing test pattern
2. Use descriptive test names
3. Include proper error handling
4. Update this README with new test scenarios
5. Ensure tests are idempotent and don't interfere with each other

## Architecture Notes

The test suite is designed to:
- Test the system as a black box through public APIs
- Validate business logic rather than implementation details
- Provide comprehensive coverage of user workflows
- Generate actionable reports for system validation
- Support continuous integration and deployment pipelines
