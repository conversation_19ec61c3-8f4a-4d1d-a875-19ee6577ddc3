import requests
from common import BASE_URL, pretty_print

def test_get_transaction_summary():
    print("Testing GET /transaction-summary")
    # Detailed Test Case:
    # This test verifies that the /transaction-summary endpoint returns a 200 OK response,
    # with the required query parameters 'start_date' and 'end_date', and includes expected aggregation buckets.
    params = {"start_date": "2025-07-01", "end_date": "2025-07-31"}
    resp = requests.get(f"{BASE_URL}/transaction-summary", params=params)
    pretty_print(resp)
    # Additional validation: check that response data includes expected buckets.
    try:
        json_data = resp.json()
        expected_buckets = ["blacklist", "watchlist", "stafflist", "limits", "normal"]
        for bucket in expected_buckets:
            if bucket not in json_data.get("data", {}):
                print(f"Warning: Missing bucket '{bucket}' in response data.")
    except Exception as e:
        print("Error parsing JSON response:", e)

def main():
    print("Running Transaction Summary API tests against:", BASE_URL)
    test_get_transaction_summary()

def test_get_transaction_summary_missing_params():
    # New Test Case:
    # Attempt to fetch transaction summary without providing required query parameters.
    # Expected: The API should respond with a 400 error indicating missing parameters.
    print("Testing transaction summary with missing query parameters")
    resp = requests.get(f"{BASE_URL}/transaction-summary")
    pretty_print(resp)

if __name__ == "__main__":
    main()
    test_get_transaction_summary_missing_params()
