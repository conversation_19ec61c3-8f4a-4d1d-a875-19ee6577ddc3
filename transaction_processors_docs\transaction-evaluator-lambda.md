# fraud-py-v1-transaction-evaluator-lambda – Technical Specification  

## 1. Purpose  
Implements the **brain of the fraud engine** – a deterministic rule-set that
tags each transaction with risk reasons across three complementary axes:

1. **Static List Checks** – low-latency DynamoDB point reads determine whether
   the account / application / merchant / product appears on curated
   *black-, watch- or staff-lists* specific to the transaction’s channel.  
2. **Impossible-Travel Heuristic** – card transactions are examined for a
   country “hop” within six hours, signalling possible card cloning.  The Lambda
   keeps the last card transaction per account in the Processed-Transactions
   table so the rule works statelessly across invocations and containers.  
3. **Dynamic Velocity Limits** – thresholds such as **hourly sum**, **daily
   count** or **single-transaction amount** are stored in the *Limits* table
   and can be tuned by operations without redeploying code.  The evaluator
   compares those thresholds against the **live aggregates** computed by the
   previous Lambda, ensuring consistent figures (no re-reads).  

Architectural highlights:

- **Two-Thread Parallelism** – list lookups and limit checks are both I/O bound
  but independent, so they are executed concurrently via
  `ThreadPoolExecutor(max_workers=2)` to squeeze latency.  
- **Side-effect-free Evaluation** – the Lambda only reads from DynamoDB except
  for the *card velocity state* write; this keeps evaluation deterministic and
  idempotent.  
- **Explainable Output** – every triggered rule inserts a key such as
  `blacklist_account`, `hourly_sum_exceeded`, `card_different_countries_6h`
  into the `evaluation` map, giving downstream analytics a human-readable audit
  trail.  
- **Precise Timing** – the evaluator stamps
  `time_transaction_evaluator_ingested` and `time_evaluated` so the total
  decision latency can be monitored over time.  

The result is a **fully-evaluated** transaction ready for long-term storage,
dashboards and real-time alerts.

## 2. Runtime Contract  
| Item | Value |
| ---- | ----- |
| **Handler** | `fraud-py-v1-transaction-evaluator-lambda.app.lambda_handler` |
| **Trigger** | SQS → `FraudPyV1SemiProcessedTransactionsQueueShard1.fifo` |
| **Output** | SQS → `FraudPyV1FinalProcessedTransactionsQueue.fifo` |
| **Reads** | DynamoDB → `FraudPyV1ListsTable`, `FraudPyV1LimitsTable`, `FraudPyV1ProcessedTransactionsTable` |

### Environment Variables  
`FRAUD_PY_V1_LISTS_TABLE_NAME`, `FRAUD_PY_V1_LIMITS_TABLE_NAME`,  
`FRAUD_PY_V1_PROCESSED_TRANSACTIONS_TABLE_NAME`,  
`FRAUD_PY_V1_FINAL_PROCESSED_TRANSACTIONS_QUEUE_URL`.

## 3. Core Evaluation Pipeline  
| Stage | Logic |
|-------|-------|
| **1. List Checks** | `evaluate_lists()` builds PK/SK pairs for *BLACK / WATCH / STAFF* across 4 entity levels; flags any hit. |
| **2. Card Velocity** | `evaluate_card_velocity()` detects card use in different countries ≤ 6 h apart (impossible travel). |
| **3. Limits** | `evaluate_limits()` retrieves threshold rows from *Limits* table per entity level. Delegates aggregate comparisons to `evaluate_aggregation_limits()` which inspects the `aggregates` map produced by the previous Lambda. |
| **4. Assemble Output** | Merges `evaluation` dict, stamps `time_evaluated`, forwards to final queue with idempotent `MessageDeduplicationId`. |

Parallelism: `ThreadPoolExecutor(max_workers=2)` runs list & limit checks concurrently.

## 4. Key Helper Functions  
| Function | Task |
|----------|------|
| `get_dynamo_item()` | ConsistentRead point lookup |
| `evaluate_lists()` | Static membership evaluation |
| `evaluate_card_velocity()` | Impossible travel detection |
| `evaluate_aggregation_limits()` | Aggregated SUM/COUNT vs thresholds |
| `evaluate_limits()` | Orchestrates limit hierarchy |

## 5. Error Handling  
* Each SQS record processed in its own `future`; individual failures logged,
  others continue.  
* DynamoDB errors bubble; would trigger Lambda retry then DLQ.
