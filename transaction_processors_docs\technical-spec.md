# FraudPy v1 – Technical Specification
*Generated by Aider – 2025-07-03*

FraudPy v1 is a fully-serverless, event-driven fraud-detection pipeline running
entirely on AWS services (Lambda, SQS FIFO, DynamoDB).  
The source tree contains **five** Python 3.11 Lambda modules, each
representing one processing stage.  
This document captures their runtime contracts, environment variables and
internal helper routines so new engineers can reason about the end-to-end
data-flow without trawling code.

---

## Table of Contents
- [fraud-py-v1-mock-producer-lambda](#fraud-py-v1-mock-producer-lambdaapp.py)
- [fraud-py-v1-transaction-router-lambda](#fraud-py-v1-transaction-router-lambdaapp.py)
- [fraud-py-v1-transaction-statistics-calculator-lambda](#fraud-py-v1-transaction-statistics-calculator-lambdaapp.py)
- [fraud-py-v1-transaction-evaluator-lambda](#fraud-py-v1-transaction-evaluator-lambdaapp.py)
- [fraud-py-v1-result-writer-lambda](#fraud-py-v1-result-writer-lambdaapp.py)

---

### fraud-py-v1-mock-producer-lambda/app.py
| Item | Details |
| ---- | ------- |
| **Handler** | `lambda_handler` |
| **Trigger** | API Gateway → `POST /submit` |
| **Input** | Raw transaction JSON supplied by external client |
| **Output** | Message → **FraudPyV1IncomingTransactionsQueue.fifo** |
| **Env Vars** | `FRAUD_PY_V1_INCOMING_TRANSACTIONS_QUEUE_URL` (SQS URL) <br> `FRAUD_PY_V1_PROCESSED_TRANSACTIONS_TABLE_NAME` (DDB lookup) |
| **Key Helpers** | `validate_transaction`, `translate_transaction_keys`, `generate_internal_transaction_id`, `retrieve_evaluation`, `create_summary_response` |
| **Error Handling** | Returns HTTP 400 for validation/JSON errors, HTTP 408 on timeout, HTTP 500 on unexpected failure |
| **Metrics** | Poll latency (client-side) logged via response `processing_time` field |

---

### fraud-py-v1-transaction-router-lambda/app.py
| Item | Details |
| ---- | ------- |
| **Handler** | `lambda_handler` |
| **Trigger** | **FraudPyV1IncomingTransactionsQueue.fifo** |
| **Input** | Validated raw transaction messages |
| **Output** | Routed message → `FraudPyV1RawTransactionsQueueShard1.fifo` (static shard *1* for now) |
| **Env Vars** | `FRAUD_PY_V1_RAW_TRANSACTIONS_QUEUE_URL_SHARD_1` <br> `FRAUD_PY_V1_ROUTING_TABLE_NAME` (future feature) |
| **Core Logic** | Picks shard, forwards message preserving FIFO ids |
| **IAM** | `AmazonSQSFullAccess`, `AmazonDynamoDBFullAccess` |
| **Failure Mode** | Raises `ValueError` when shard URL missing → SQS auto-retry then DLQ |

---

### fraud-py-v1-transaction-statistics-calculator-lambda/app.py
| Item | Details |
| ---- | ------- |
| **Handler** | `lambda_handler` |
| **Trigger** | `FraudPyV1RawTransactionsQueueShard1.fifo` |
| **Input** | Raw transaction JSON |
| **Output** | Semi-processed message → `FraudPyV1SemiProcessedTransactionsQueueShard1.fifo` |
| **Env Vars** | `FRAUD_PY_V1_SEMI_PROCESSED_TRANSACTIONS_QUEUE_URL` <br> `FRAUD_PY_V1_AGGREGATIONS_TABLE_NAME` |
| **Important Constants** | `MAX_CONCURRENT_UPDATES = 10` <br> `OPTIMISTIC_LOCKING_MAX_RETRIES = 5` |
| **Key Helpers** | `generate_aggregation_keys`, `update_dynamo_aggregate`, `process_transaction_aggregates`, `DecimalEncoder` |
| **Concurrency** | `ThreadPoolExecutor` to issue parallel conditional `update_item` calls with exponential back-off |
| **Data Contract** | Adds `aggregates` map + two timing fields: `time_statistics_calculator_ingested`, `time_statistics_calculated` |

---

### fraud-py-v1-transaction-evaluator-lambda/app.py
| Item | Details |
| ---- | ------- |
| **Handler** | `lambda_handler` |
| **Trigger** | `FraudPyV1SemiProcessedTransactionsQueueShard1.fifo` |
| **Input** | Semi-processed transaction containing `aggregates` |
| **Output** | Evaluated message → `FraudPyV1FinalProcessedTransactionsQueue.fifo` |
| **Env Vars** | `FRAUD_PY_V1_FINAL_PROCESSED_TRANSACTIONS_QUEUE_URL` <br> `FRAUD_PY_V1_LISTS_TABLE_NAME` <br> `FRAUD_PY_V1_LIMITS_TABLE_NAME` <br> `FRAUD_PY_V1_PROCESSED_TRANSACTIONS_TABLE_NAME` |
| **Evaluation Stages** | 1. `evaluate_lists` (black/watch/staff) <br> 2. `evaluate_limits` (single-tx + aggregate limits) <br> 3. `evaluate_card_velocity` (impossible travel) |
| **Key Helpers** | `get_dynamo_item`, `evaluate_lists`, `evaluate_limits`, `evaluate_aggregation_limits`, `evaluate_card_velocity` |
| **Timing Fields** | Adds `time_transaction_evaluator_ingested`, `time_evaluated` |
| **Output Schema** | Merges original transaction, `aggregates`, and `evaluation` dict (keys like `black_account`, `hourly_sum_exceeded`) |

---

### fraud-py-v1-result-writer-lambda/app.py
| Item | Details |
| ---- | ------- |
| **Handler** | `lambda_handler` |
| **Trigger** | `FraudPyV1FinalProcessedTransactionsQueue.fifo` |
| **Input** | Fully evaluated transaction JSON |
| **Persistence** | Writes denormalised items into **FraudPyV1ProcessedTransactionsTable** |
| **Env Vars** | `FRAUD_PY_V1_PROCESSED_TRANSACTIONS_TABLE_NAME` |
| **Denormalisation Strategy** | Generates ≥ 1 item per tx via `generate_dynamo_items`: <br> • **Master record** `PARTITION_KEY=EVALUATED` <br> • Channel /\* entity scoped PKs <br> • Conditional PKs for each evaluation type using `EVALUATION_KEY_MAP` |
| **Latency Metrics** | `log_latency_metrics` prints ms durations for calculation, evaluation and total pipeline |
| **Batch Write** | Uses `Table.batch_writer()` (automatic retries) |
| **Error Handling** | Per-record try/except ensures one bad message won’t poison the whole batch |

---

## Data Flow (high-level)
```
Client → POST /submit
        ↓
Mock-Producer Lambda ─┐
        SQS FIFO      │
        ↓             │
Transaction Router ───┤→ Fan-out (future shards)
        SQS FIFO      │
        ↓             │
Statistics-Calculator │→ DynamoDB *Aggregations*
        SQS FIFO      │
        ↓             │
Transaction Evaluator │→ DynamoDB *Lists*, *Limits*
        SQS FIFO      │
        ↓             │
Result Writer ────────┘→ DynamoDB *ProcessedTransactions*
```

---

## Future Work
1. Replace `Amazon*FullAccess` with least-privilege IAM.
2. Parameterise number of shards & queues via CloudFormation `Parameters`.
3. Emit custom CloudWatch metrics for each latency measurement so Datadog can
   scrape via `aws-cloudwatch-metrics` integration.
