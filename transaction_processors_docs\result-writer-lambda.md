# fraud-py-v1-result-writer-lambda – Technical Specification  

## 1. Purpose  
Serves as the **commit stage** that turns an evaluated transaction into an
immutable audit trail, optimised for both analytical queries and low-latency
API look-ups.

Deep-dive on responsibilities:

- **Latency Telemetry** – before any write the Lambda computes three durations
  (`calc_latency`, `eval_latency`, `total_latency`) and logs them in a Grep-able
  format `LATENCY: <stage> took <ms> ms`; ops can create CloudWatch Insights
  queries or Metric Filters without touching code.  
- **Sophisticated Denormalisation** – `generate_dynamo_items()` explodes a
  single JSON into **multiple partition keys** so diverse query patterns become
  a simple PartiQL `WHERE PARTITION_KEY = ...` scan – no secondary indexes
  required.  Conditional keys are only created when the evaluation actually
  triggered, keeping the table lean.  
- **Time-series Sort Keys** – the time-scoped items store
  `<unix_ts>_<transaction_id>` as `SORT_KEY` so analysts can paginate recent
  transactions chronologically with *one query per scope* (channel / account /
  merchant / product).  
- **Idempotent Batch Writes** – by using `Table.batch_writer()` with consistent
  partition/sort keys the Lambda can be retried or partially fail without
  producing duplicate records.  DynamoDB handles per-item retries automatically
  under the hood.  
- **Cold-start Optimisation** – the DynamoDB `Table` resource is instantiated
  once per container reuse, avoiding extra `DescribeTable` calls.  
- **Operational Resilience** – any malformed message only affects its own
  record; the rest of the SQS batch continues processing, and the poison
  message will bubble to the queue’s DLQ for later inspection.

In essence, the Result-Writer Lambda converts the pipeline’s transient SQS
message into a permanent, query-friendly, observability-rich data artefact.

## 2. Runtime Contract  
| Item | Value |
| ---- | ----- |
| **Handler** | `fraud-py-v1-result-writer-lambda.app.lambda_handler` |
| **Trigger** | SQS → `FraudPyV1FinalProcessedTransactionsQueue.fifo` |
| **Writes** | DynamoDB → `FraudPyV1ProcessedTransactionsTable` |

### Environment Variables  
`FRAUD_PY_V1_PROCESSED_TRANSACTIONS_TABLE_NAME`

## 3. Core Logic Flow  
1. **Deserialise** SQS `record['body']` JSON.  
2. **Latency Metrics** – `log_latency_metrics()` calculates:  
   * `calc_latency` (aggregator stage)  
   * `eval_latency` (evaluator stage)  
   * `total_latency` (end-to-end)  
   Logs as: `LATENCY: <label> took <ms> ms`.  
3. **Denormalisation** – `generate_dynamo_items(transaction)` yields:  
   * **Master**: `PARTITION_KEY=EVALUATED`, `SORT_KEY=<tx_id>`  
   * **Time-series**: timestamped SK for channel, account, app, merchant, product scopes  
   * **Conditional** scopes (BLACKLIST, LIMIT …) only if applicable per `evaluation` dict.  
4. **Batch Persist** – `Table.batch_writer()` writes all items with automatic retries.  
5. **Success Log** – prints confirmation with `tx_id`.

## 4. Helper Function Details  
| Function | Highlights |
|----------|------------|
| `parse_iso8601()` | RFC 3339 tolerant parser → `datetime` |
| `log_latency_metrics()` | Converts seconds → ms and logs |
| `generate_dynamo_items()` | Constructs PK/SK permutations & JSON-encodes payload |

## 5. Error Handling  
* Per-record `try/except` ensures one bad message does not fail whole batch.  
* JSON decode errors & unexpected exceptions logged with `exc_info`.
