"""
Comprehensive tests for the **Case-Management** endpoints.

Run:

    python scripts\\case_management_test.py
"""
import uuid
import requests
from common import BASE_URL, pretty_print


def test_create_case() -> str:
    # Detailed Test Case:
    # Sends a POST request to /case with a unique transaction ID payload.
    # Expected: 200 OK with a response message confirming the case creation along with a generated case_id.
    txn_id = f"TEST-{uuid.uuid4()}"
    payload = {
        "transaction_id": txn_id,
        "assigned_to": "<EMAIL>",
        "status": "OPEN"
    }
    print("Testing create_case:", txn_id)
    resp = requests.post(f"{BASE_URL}/case", json=payload)
    pretty_print(resp)
    return txn_id


def test_duplicate_create_case(txn_id: str) -> None:
    # Detailed Test Case:
    # Attempts to create a duplicate case using the same transaction_id.
    # Expected: Ideally, the API should reject this with an error (e.g., 409 Conflict), but if it returns 200 OK, it indicates a potential issue with duplicate detection.
    print("Testing duplicate create_case for:", txn_id)
    payload = {
        "transaction_id": txn_id,
        "assigned_to": "<EMAIL>",
        "status": "OPEN"
    }
    resp = requests.post(f"{BASE_URL}/case", json=payload)
    pretty_print(resp)


def test_update_case_status_valid(txn_id: str) -> None:
    # Detailed Test Case:
    # Updates the case status from "OPEN" to "IN_PROGRESS" with a valid payload.
    # Expected: 200 OK with an updated timestamp, and the case details reflecting the new status.
    print("Testing update_case_status (valid) for:", txn_id)
    payload = {
        "transaction_id": txn_id,
        "assigned_to": "<EMAIL>",
        "status": "IN_PROGRESS"
    }
    resp = requests.put(f"{BASE_URL}/case/status", json=payload)
    pretty_print(resp)


def test_update_case_status_invalid(txn_id: str) -> None:
    # Detailed Test Case:
    # Attempts to update the case status to an invalid value.
    # Expected: The API should reject this update with a 400 error.
    print("Testing update_case_status (invalid) for:", txn_id, "- expecting 400 error")
    payload = {
        "transaction_id": txn_id,
        "assigned_to": "<EMAIL>",
        "status": "INVALID_STATUS"
    }
    resp = requests.put(f"{BASE_URL}/case/status", json=payload)
    if resp.status_code == 400:
        print("Received expected 400 error for invalid status update.")
    else:
        print("Unexpected status code for invalid status update:", resp.status_code)
    pretty_print(resp)


def test_get_case(txn_id: str) -> None:
    # Detailed Test Case:
    # Retrieves the case using a GET request with the provided transaction_id.
    # Expected: 200 OK with the case details reflecting the current status, assigned investigator, and timestamps,
    # or 404 if the case is not found.
    print("Testing get_case for:", txn_id)
    resp = requests.get(f"{BASE_URL}/case", params={"transaction_id": txn_id})
    pretty_print(resp)


def test_close_case(txn_id: str) -> None:
    # Detailed Test Case:
    # Sends a PUT request to close the case identified by transaction_id.
    # Expected: 200 OK indicating the case has been moved to a closed state.
    print("Testing close_case for:", txn_id)
    payload = {"transaction_id": txn_id}
    resp = requests.put(f"{BASE_URL}/case/close", json=payload)
    pretty_print(resp)


def test_post_report(txn_id: str) -> None:
    # Detailed Test Case:
    # Attaches a report to the case by sending a POST request to the /report endpoint.
    # Expected: 200 OK with a composite report ID in the response.
    print("Testing post_report for:", txn_id)
    payload = {"transaction_id": txn_id}
    resp = requests.post(f"{BASE_URL}/report", json=payload)
    pretty_print(resp)


def test_get_open_cases() -> None:
    # Detailed Test Case:
    # Retrieves a paginated list of open cases.
    # Expected: 200 OK with response data containing an array of cases currently in the open state.
    print("Testing get_open_cases")
    resp = requests.get(f"{BASE_URL}/cases/open")
    pretty_print(resp)


def test_get_closed_cases() -> None:
    # Detailed Test Case:
    # Retrieves a paginated list of closed cases.
    # Expected: 200 OK with response data containing an array of cases now in the closed state.
    print("Testing get_closed_cases")
    resp = requests.get(f"{BASE_URL}/cases/closed")
    pretty_print(resp)


def main() -> None:
    # Detailed Test Cases for Case Management API:
    # This suite covers the full lifecycle of a case:
    # 1. Creation of a new case (expect 200 OK with a generated case_id).
    # 2. Duplicate case creation (should ideally be rejected, but here returns 200 OK, which needs review).
    # 3. Valid status update from OPEN to IN_PROGRESS (expect 200 OK with updated timestamp).
    # 4. Invalid status update (should be rejected, but here returns 200 OK, indicating a logic issue).
    # 5. Retrieval of case details after status updates (confirming state changes).
    # 6. Attachment of a report to the case (expect 200 OK with a composite report_id).
    # 7. Closing the case (expect 200 OK and subsequent get returns 404, indicating the case is moved).
    # 8. Listing open and closed cases (verifying correct retrieval and pagination).
    # 9. Additional negative tests: querying with missing parameters and creating cases with incomplete data.
    print("Running comprehensive Case-Management API tests against:", BASE_URL)
    # Create a new case
    txn_id = test_create_case()
    
    # Test duplicate creation
    test_duplicate_create_case(txn_id)
    
    # Test updating case status with a valid transition
    test_update_case_status_valid(txn_id)
    
    # Test updating case status with an invalid status
    test_update_case_status_invalid(txn_id)
    
    # Retrieve the case after status update
    test_get_case(txn_id)
    
    # Attach a report to the case
    test_post_report(txn_id)
    
    # Close the case
    test_close_case(txn_id)
    
    # Retrieve the case after closing
    test_get_case(txn_id)
    
    # List open cases
    test_get_open_cases()
    
    # List closed cases
    test_get_closed_cases()
    
    # Additional negative test cases:
    print("Testing get_case with missing transaction_id (expected to fail with a 400 or 404)")
    resp = requests.get(f"{BASE_URL}/case")
    pretty_print(resp)
    
    print("Testing create_case with missing required fields (expected to fail)")
    payload = {"assigned_to": "<EMAIL>"}  # Missing transaction_id and status
    resp = requests.post(f"{BASE_URL}/case", json=payload)
    pretty_print(resp)
    
    
def test_get_nonexistent_case():
    # New Test Case:
    # Attempt to retrieve a case that does not exist.
    # Expected: The API should respond with 404 Not Found or an error message.
    print("Testing get non-existent case")
    resp = requests.get(f"{BASE_URL}/case", params={"transaction_id": "NON_EXISTENT"})
    pretty_print(resp)

if __name__ == "__main__":
    main()
    test_get_nonexistent_case()
