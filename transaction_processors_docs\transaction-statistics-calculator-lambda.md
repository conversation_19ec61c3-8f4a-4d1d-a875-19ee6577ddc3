# fraud-py-v1-transaction-statistics-calculator-lambda – Technical Specification  

## 1. Purpose  
Maintains **near-real-time velocity statistics** so that fraud rules can reason
about behavioural patterns, not just single transactions.  It achieves this
with a clever combination of DynamoDB conditional updates and multi-threaded
fan-out:

- **Time-Series Windows** – for every transaction four calendar windows are
  updated (`MONTH`, `WEEK`, `DAY`, `HOUR`).  The windows are stored as part of
  the *SORT_KEY* so TTL or time-bounded queries can prune old data efficiently.  
- **Multi-Level Entity Granularity** – statistics exist at four business
  levels:  
  **ACCOUNT → ACCOUNT+APP → ACCOUNT+APP+MERCHANT → ACCOUNT+APP+MERCHANT+PRODUCT**  
  giving fraud analysts flexibility when crafting rules (e.g., *daily sum per
  merchant*).  
- **Optimistic Locking** – each DynamoDB item stores a `VERSION` counter.
  Updates use `ConditionExpression` to atomically increment `SUM`, `COUNT` and
  `VERSION`, retrying with exponential back-off on conflict.  This removes the
  need for a central lock service while still preventing lost updates under
  high concurrency.  
- **ThreadPoolExecutor Parallelism** – updating 16 items per transaction could
  take hundreds of milliseconds sequentially.  By fanning out the writes over
  **10 threads** the Lambda achieves sub-50 ms aggregate update latency even
  under hot-key pressure.  
- **Decimal-Safe JSON** – DynamoDB returns numbers as `Decimal`.  A custom
  `DecimalEncoder` converts them to strings during JSON serialisation so that
  downstream Lambdas keep full precision without runtime type errors.  
- **Latency Instrumentation** – timestamps
  `time_statistics_calculator_ingested` and `time_statistics_calculated`
  surround the compute section, enabling the Result-Writer to compute stage
  latency during final persistence.  

The output is an **enriched payload** that merges the original transaction with
its freshly updated velocity aggregates, ready for deterministic fraud
evaluation in the next stage.

## 2. Runtime Contract  
| Item | Value |
| ---- | ----- |
| **Handler** | `fraud-py-v1-transaction-statistics-calculator-lambda.app.lambda_handler` |
| **Trigger** | SQS → `FraudPyV1RawTransactionsQueueShard1.fifo` |
| **Output** | SQS → `FraudPyV1SemiProcessedTransactionsQueueShard1.fifo` |
| **State** | DynamoDB Table → `FraudPyV1AggregationsTable` |

### Environment Variables  
| Name | Description |
|------|-------------|
| `FRAUD_PY_V1_AGGREGATIONS_TABLE_NAME` | Target table |
| `FRAUD_PY_V1_SEMI_PROCESSED_TRANSACTIONS_QUEUE_URL` | Next-stage queue URL |

## 3. Core Processing Steps  
1. **Deserialise** record JSON → `transaction` dict.  
2. **Aggregation Key Generation** – `generate_aggregation_keys(transaction)` yields
   16 PK/SK pairs (4 time windows × 4 entity levels).  
3. **Parallel Updates** – `ThreadPoolExecutor(max_workers=10)` runs
   `update_dynamo_aggregate()` for each key:  
   * Uses conditional expression `VERSION = :current` for optimistic locking.  
   * Retries **5** times with exponential back-off (0.1 s × 2ⁿ).  
4. **Collect Aggregates** – returns updated `SUM`, `COUNT`, `VERSION` maps.  
5. **Enrich Payload** – attaches `aggregates` and timing fields
   `time_statistics_calculator_ingested` / `time_statistics_calculated`.  
6. **Forward** – `send_to_output_queue()` publishes JSON with
   `MessageGroupId="FraudPyV1StatisticsCalculatorLambdaGroup"`.

## 4. Notable Helpers  
| Function | Responsibility |
|----------|----------------|
| `DecimalEncoder` | Serialises `decimal.Decimal` to string |
| `update_dynamo_aggregate()` | Atomic counter update & optimistic locking |
| `process_transaction_aggregates()` | Orchestrates threaded updates |

## 5. Failure Modes  
* Conditional write conflicts retried; final failure logs error but continues.  
* Any unhandled exception per record is caught, logged, and the batch moves on
  (no explicit DLQ yet).
