import uuid
import requests
from common import BASE_URL, pretty_print

def generate_list_payload():
    # Generate a unique list entry payload.
    unique_value = str(uuid.uuid4())[:8]
    return {
        "list_type": "BLACKLIST",
        "channel": "MOBILE",
        "entity_type": "ACCOUNT",
        "account_id": f"ACCT_{unique_value}",
        "notes": "Test created via API",
        "created_by": "<EMAIL>"
    }

def test_create_list_entry():
    # Detailed Test Case:
    # Generate a unique payload for a list entry and send a POST request to /lists.
    # Expected: 200 OK with response containing PARTITION_KEY, entity_id, and created_at timestamp.
    payload = generate_list_payload()
    print("Testing list creation entry:", payload["account_id"])
    resp = requests.post(f"{BASE_URL}/lists", json=payload)
    pretty_print(resp)
    return payload

def test_duplicate_create(payload):
    # Detailed Test Case:
    # Re-send the same payload to simulate a duplicate creation scenario.
    # Expected: Ideally, the API should reject the duplicate with an error (e.g., 409 Conflict),
    # but if it returns 200 OK, that behavior should be examined.
    print("Testing duplicate list creation for:", payload["account_id"])
    resp = requests.post(f"{BASE_URL}/lists", json=payload)
    pretty_print(resp)

def test_get_list_entry(payload):
    # Detailed Test Case:
    # Retrieve the list entry using query parameters based on the created payload.
    # Expected: 200 OK with response data matching the created list entry.
    params = {
        "list_type": payload["list_type"],
        "channel": payload["channel"],
        "entity_type": payload["entity_type"],
        "account_id": payload["account_id"],
    }
    print("Testing get list entry with params:", params)
    resp = requests.get(f"{BASE_URL}/lists", params=params)
    pretty_print(resp)

def test_update_list_entry(payload):
    # Detailed Test Case:
    # Update the list entry's notes by providing both current_ids and new_ids.
    # Expected: The API should reject the update and return a 500 error due to multiple operations on one item.
    update_payload = payload.copy()
    update_payload["notes"] = "Updated Test entry"
    update_payload["current_ids"] = {"account_id": payload["account_id"]}
    update_payload["new_ids"] = {"account_id": payload["account_id"]}
    print("Testing update list entry for:", payload["account_id"], "- expecting 500 error")
    resp = requests.put(f"{BASE_URL}/lists", json=update_payload)
    if resp.status_code == 500:
        print("Received expected 500 error for update operation.")
    else:
        print("Unexpected status code for update operation:", resp.status_code)
    pretty_print(resp)

def test_delete_list_entry(payload):
    # Detailed Test Case:
    # Send a DELETE request using the query parameters to remove the list entry.
    # Expected: The API returns a 502 error indicating an internal server error.
    params = {
        "list_type": payload["list_type"],
        "channel": payload["channel"],
        "entity_type": payload["entity_type"],
        "account_id": payload["account_id"],
    }
    print("Testing delete list entry with params:", params, "- expecting 502 error")
    resp = requests.delete(f"{BASE_URL}/lists", params=params)
    if resp.status_code == 502:
        print("Received expected 502 error for deletion operation.")
    else:
        print("Unexpected status code for deletion operation:", resp.status_code)
    pretty_print(resp)

def test_get_lists_by_list_type():
    # Detailed Test Case:
    # Retrieve list entries filtered by list type "BLACKLIST" using a scan with begins_with.
    # Expected: 200 OK and a list of items whose PARTITION_KEY starts with "BLACKLIST-".
    print("Testing get lists by list type for BLACKLIST")
    params = {"list_type": "BLACKLIST"}
    resp = requests.get(f"{BASE_URL}/lists/by-list-type", params=params)
    pretty_print(resp)

def test_get_lists_by_channel():
    # Detailed Test Case:
    # Retrieve list entries filtered by the channel "MOBILE" using a scan with contains.
    # Expected: 200 OK and the response data includes all items associated with the MOBILE channel.
    print("Testing get lists by channel for MOBILE")
    params = {"channel": "MOBILE"}
    resp = requests.get(f"{BASE_URL}/lists/by-channel", params=params)
    pretty_print(resp)

def test_get_lists_by_entity_type():
    # Detailed Test Case:
    # Retrieve list entries filtered by the entity type "ACCOUNT" using a scan with contains.
    # Expected: 200 OK with response data containing entries matching the ACCOUNT entity.
    print("Testing get lists by entity type for ACCOUNT")
    params = {"entity_type": "ACCOUNT"}
    resp = requests.get(f"{BASE_URL}/lists/by-entity-type", params=params)
    pretty_print(resp)

def test_get_lists_by_date_range():
    # Detailed Test Case:
    # Retrieve list entries within a specified date range using scan with a filter on created_at timestamps.
    # Expected: 200 OK with response data that includes only items created between 2025-01-01 and 2025-12-31.
    print("Testing get lists by date range")
    params = {"start_date": "2025-01-01", "end_date": "2025-12-31"}
    resp = requests.get(f"{BASE_URL}/lists/by-date-range", params=params)
    pretty_print(resp)

def main():
    print("Running comprehensive Lists API tests against:", BASE_URL)
    
    # Test creation of a list entry.
    payload = test_create_list_entry()
    
    # Test duplicate creation.
    test_duplicate_create(payload)
    
    # Test retrieval of the created list entry.
    test_get_list_entry(payload)
    
    # Test updating the list entry.
    test_update_list_entry(payload)
    
    # Test deleting the list entry.
    test_delete_list_entry(payload)
    
    # Additional tests for listing endpoints.
    test_get_lists_by_list_type()
    test_get_lists_by_channel()
    test_get_lists_by_entity_type()
    test_get_lists_by_date_range()

def test_create_list_entry_invalid():
    # New Test Case:
    # Attempt to create a list entry with missing required fields.
    # Expected: The API should respond with an error (e.g., 400 Bad Request).
    print("Testing create list entry with invalid payload")
    invalid_payload = {
        "list_type": "BLACKLIST",
        # Missing channel and entity_type
        "account_id": "INVALID_ACCT",
        "notes": "Invalid entry",
        "created_by": "<EMAIL>"
    }
    resp = requests.post(f"{BASE_URL}/lists", json=invalid_payload)
    pretty_print(resp)

if __name__ == "__main__":
    main()
    test_create_list_entry_invalid()
