"""
Comprehensive End-to-End Test Suite for FraudPy System

This test suite validates the complete business use case of the fraud detection system:
1. Transaction submission and processing pipeline
2. Fraud rule evaluation (lists, limits, card velocity)
3. Case management workflow
4. Dashboard analytics and reporting

The tests simulate real-world scenarios including:
- Normal transactions that pass all checks
- Fraudulent transactions that trigger various rules
- Case investigation workflow
- Analytics and reporting functionality

Run with: python tests/comprehensive_fraud_system_test.py
"""

import json
import time
import uuid
import requests
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import sys
import os
from pathlib import Path

# Add scripts directory to path to import common utilities
sys.path.append(str(Path(__file__).parent.parent / "scripts"))
from common import BASE_URL, pretty_print


class FraudSystemTestSuite:
    """Comprehensive test suite for the FraudPy fraud detection system."""
    
    def __init__(self):
        self.base_url = BASE_URL
        self.test_data = {}
        self.test_results = []
        
    def log_test_result(self, test_name: str, success: bool, details: str = ""):
        """Log test results for reporting."""
        result = {
            "test_name": test_name,
            "success": success,
            "details": details,
            "timestamp": datetime.now().isoformat()
        }
        self.test_results.append(result)
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status}: {test_name}")
        if details:
            print(f"   Details: {details}")
    
    def generate_test_transaction(self, 
                                account_id: str = None,
                                application_id: str = "APP001",
                                merchant_id: str = "MERCH001", 
                                product_id: str = "PROD001",
                                amount: float = 100.0,
                                currency: str = "GHS",
                                country: str = "GH",
                                channel: str = "MOBILE") -> Dict[str, Any]:
        """Generate a test transaction with realistic data."""
        if not account_id:
            account_id = f"ACCT{uuid.uuid4().hex[:8].upper()}"
            
        return {
            "transaction_id": f"TXN{uuid.uuid4().hex[:12].upper()}",
            "processor": application_id,  # External field name
            "account_ref": account_id,    # External field name  
            "merchant_id": merchant_id,
            "product_id": product_id,
            "amount": amount,
            "currency": currency,
            "country": country,
            "channel": channel,
            "date": datetime.now().isoformat(),
            "description": f"Test transaction for {account_id}"
        }
    
    def submit_transaction(self, transaction: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Submit a transaction to the fraud processing pipeline."""
        try:
            response = requests.post(f"{self.base_url}/submit", json=transaction)
            if response.status_code == 200:
                return response.json()
            else:
                print(f"Transaction submission failed: {response.status_code}")
                pretty_print(response)
                return None
        except Exception as e:
            print(f"Error submitting transaction: {e}")
            return None
    
    def wait_for_transaction_processing(self, transaction_id: str, timeout: int = 30) -> bool:
        """Wait for transaction to be processed and available in dashboard."""
        start_time = time.time()
        while time.time() - start_time < timeout:
            # Check if transaction appears in evaluated transactions
            today = datetime.now().strftime("%Y-%m-%d")
            params = {
                "start_date": today,
                "end_date": today,
                "query_type": "all"
            }
            
            try:
                response = requests.get(f"{self.base_url}/evaluated-transactions", params=params)
                if response.status_code == 200:
                    data = response.json().get("data", [])
                    for txn in data:
                        if txn.get("transaction_id") == transaction_id:
                            return True
            except Exception as e:
                print(f"Error checking transaction status: {e}")
            
            time.sleep(2)
        
        return False

    # ==================== SETUP AND TEARDOWN METHODS ====================

    def setup_test_data(self):
        """Set up test data including lists and limits for testing."""
        print("\n🔧 Setting up test data...")

        # Create test accounts for different scenarios
        self.test_data = {
            "normal_account": f"ACCT{uuid.uuid4().hex[:8].upper()}",
            "blacklist_account": f"ACCT{uuid.uuid4().hex[:8].upper()}",
            "watchlist_account": f"ACCT{uuid.uuid4().hex[:8].upper()}",
            "staff_account": f"ACCT{uuid.uuid4().hex[:8].upper()}",
            "limit_test_account": f"ACCT{uuid.uuid4().hex[:8].upper()}",
            "velocity_test_account": f"ACCT{uuid.uuid4().hex[:8].upper()}"
        }

        # Add accounts to appropriate lists
        self._setup_blacklist()
        self._setup_watchlist()
        self._setup_stafflist()
        self._setup_limits()

        print("✅ Test data setup complete")

    def _setup_blacklist(self):
        """Add test account to blacklist."""
        payload = {
            "list_type": "BLACKLIST",
            "channel": "MOBILE",
            "entity_type": "ACCOUNT",
            "account_id": self.test_data["blacklist_account"],
            "notes": "Test account for blacklist validation",
            "created_by": "automated_test"
        }

        response = requests.post(f"{self.base_url}/lists", json=payload)
        if response.status_code in [200, 409]:  # 409 if already exists
            print(f"✅ Blacklist entry created for {self.test_data['blacklist_account']}")
        else:
            print(f"❌ Failed to create blacklist entry: {response.status_code}")
            pretty_print(response)

    def _setup_watchlist(self):
        """Add test account to watchlist."""
        payload = {
            "list_type": "WATCHLIST",
            "channel": "MOBILE",
            "entity_type": "ACCOUNT",
            "account_id": self.test_data["watchlist_account"],
            "notes": "Test account for watchlist validation",
            "created_by": "automated_test"
        }

        response = requests.post(f"{self.base_url}/lists", json=payload)
        if response.status_code in [200, 409]:
            print(f"✅ Watchlist entry created for {self.test_data['watchlist_account']}")
        else:
            print(f"❌ Failed to create watchlist entry: {response.status_code}")
            pretty_print(response)

    def _setup_stafflist(self):
        """Add test account to stafflist."""
        payload = {
            "list_type": "STAFFLIST",
            "channel": "MOBILE",
            "entity_type": "ACCOUNT",
            "account_id": self.test_data["staff_account"],
            "notes": "Test account for stafflist validation",
            "created_by": "automated_test"
        }

        response = requests.post(f"{self.base_url}/lists", json=payload)
        if response.status_code in [200, 409]:
            print(f"✅ Stafflist entry created for {self.test_data['staff_account']}")
        else:
            print(f"❌ Failed to create stafflist entry: {response.status_code}")
            pretty_print(response)

    def _setup_limits(self):
        """Set up transaction limits for testing."""
        payload = {
            "channel": "MOBILE",
            "account_id": self.test_data["limit_test_account"],
            "AMOUNT": 50,  # Single transaction limit
            "HOURLY_SUM": 200,
            "DAILY_SUM": 1000,
            "WEEKLY_SUM": 5000,
            "MONTHLY_SUM": 20000,
            "HOURLY_COUNT": 5,
            "DAILY_COUNT": 20,
            "WEEKLY_COUNT": 100,
            "MONTHLY_COUNT": 400
        }

        response = requests.post(f"{self.base_url}/limits/account", json=payload)
        if response.status_code == 200:
            print(f"✅ Limits created for {self.test_data['limit_test_account']}")
        else:
            print(f"❌ Failed to create limits: {response.status_code}")
            pretty_print(response)

    # ==================== TRANSACTION PROCESSING TESTS ====================

    def test_normal_transaction_processing(self):
        """Test that normal transactions are processed successfully without triggering fraud rules."""
        print("\n🧪 Testing normal transaction processing...")

        transaction = self.generate_test_transaction(
            account_id=self.test_data["normal_account"],
            amount=25.0  # Below any limits
        )

        # Submit transaction
        result = self.submit_transaction(transaction)
        if not result:
            self.log_test_result("Normal Transaction Submission", False, "Failed to submit transaction")
            return

        self.log_test_result("Normal Transaction Submission", True, f"Transaction {transaction['transaction_id']} submitted")

        # Wait for processing
        if self.wait_for_transaction_processing(transaction['transaction_id']):
            self.log_test_result("Normal Transaction Processing", True, "Transaction processed successfully")

            # Verify transaction appears in dashboard with no evaluation flags
            self._verify_transaction_evaluation(transaction['transaction_id'], expected_evaluation={})
        else:
            self.log_test_result("Normal Transaction Processing", False, "Transaction not processed within timeout")

    def test_blacklist_transaction_blocking(self):
        """Test that transactions from blacklisted accounts are properly flagged."""
        print("\n🧪 Testing blacklist transaction blocking...")

        transaction = self.generate_test_transaction(
            account_id=self.test_data["blacklist_account"],
            amount=25.0
        )

        result = self.submit_transaction(transaction)
        if not result:
            self.log_test_result("Blacklist Transaction Submission", False, "Failed to submit transaction")
            return

        self.log_test_result("Blacklist Transaction Submission", True, f"Transaction {transaction['transaction_id']} submitted")

        # Wait for processing
        if self.wait_for_transaction_processing(transaction['transaction_id']):
            # Verify transaction is flagged for blacklist
            expected_evaluation = {"blacklist_account": True}  # Expected evaluation structure
            self._verify_transaction_evaluation(transaction['transaction_id'], expected_evaluation)
            self.log_test_result("Blacklist Detection", True, "Blacklist account properly flagged")
        else:
            self.log_test_result("Blacklist Detection", False, "Transaction not processed within timeout")

    def test_watchlist_transaction_flagging(self):
        """Test that transactions from watchlisted accounts are flagged for review."""
        print("\n🧪 Testing watchlist transaction flagging...")

        transaction = self.generate_test_transaction(
            account_id=self.test_data["watchlist_account"],
            amount=25.0
        )

        result = self.submit_transaction(transaction)
        if not result:
            self.log_test_result("Watchlist Transaction Submission", False, "Failed to submit transaction")
            return

        self.log_test_result("Watchlist Transaction Submission", True, f"Transaction {transaction['transaction_id']} submitted")

        if self.wait_for_transaction_processing(transaction['transaction_id']):
            expected_evaluation = {"watchlist_account": True}
            self._verify_transaction_evaluation(transaction['transaction_id'], expected_evaluation)
            self.log_test_result("Watchlist Detection", True, "Watchlist account properly flagged")
        else:
            self.log_test_result("Watchlist Detection", False, "Transaction not processed within timeout")

    def test_staff_transaction_exemption(self):
        """Test that transactions from staff accounts are exempt from fraud rules."""
        print("\n🧪 Testing staff transaction exemption...")

        transaction = self.generate_test_transaction(
            account_id=self.test_data["staff_account"],
            amount=25.0
        )

        result = self.submit_transaction(transaction)
        if not result:
            self.log_test_result("Staff Transaction Submission", False, "Failed to submit transaction")
            return

        self.log_test_result("Staff Transaction Submission", True, f"Transaction {transaction['transaction_id']} submitted")

        if self.wait_for_transaction_processing(transaction['transaction_id']):
            expected_evaluation = {"stafflist_account": True}
            self._verify_transaction_evaluation(transaction['transaction_id'], expected_evaluation)
            self.log_test_result("Staff Exemption", True, "Staff account properly identified")
        else:
            self.log_test_result("Staff Exemption", False, "Transaction not processed within timeout")

    def test_transaction_limits_enforcement(self):
        """Test that transaction limits are properly enforced."""
        print("\n🧪 Testing transaction limits enforcement...")

        # Test single transaction amount limit
        transaction = self.generate_test_transaction(
            account_id=self.test_data["limit_test_account"],
            amount=75.0  # Above the 50 limit we set
        )

        result = self.submit_transaction(transaction)
        if not result:
            self.log_test_result("Limit Exceeded Transaction Submission", False, "Failed to submit transaction")
            return

        self.log_test_result("Limit Exceeded Transaction Submission", True, f"Transaction {transaction['transaction_id']} submitted")

        if self.wait_for_transaction_processing(transaction['transaction_id']):
            # Should be flagged for amount limit exceeded
            expected_evaluation = {"amount_exceeded": True}  # Adjust based on actual evaluation structure
            self._verify_transaction_evaluation(transaction['transaction_id'], expected_evaluation)
            self.log_test_result("Amount Limit Detection", True, "Amount limit properly enforced")
        else:
            self.log_test_result("Amount Limit Detection", False, "Transaction not processed within timeout")

    def test_velocity_limits_enforcement(self):
        """Test velocity limits by submitting multiple transactions."""
        print("\n🧪 Testing velocity limits enforcement...")

        account_id = self.test_data["limit_test_account"]
        transactions = []

        # Submit multiple small transactions to trigger count limits
        for _ in range(3):
            transaction = self.generate_test_transaction(
                account_id=account_id,
                amount=10.0  # Small amount, below single transaction limit
            )

            result = self.submit_transaction(transaction)
            if result:
                transactions.append(transaction['transaction_id'])
                time.sleep(1)  # Small delay between transactions

        if len(transactions) > 0:
            self.log_test_result("Multiple Transaction Submission", True, f"Submitted {len(transactions)} transactions")

            # Check if any triggered velocity limits
            for txn_id in transactions:
                if self.wait_for_transaction_processing(txn_id):
                    self._verify_transaction_evaluation(txn_id, {})  # May or may not trigger limits
        else:
            self.log_test_result("Multiple Transaction Submission", False, "Failed to submit velocity test transactions")

    # ==================== CASE MANAGEMENT TESTS ====================

    def test_case_management_workflow(self):
        """Test the complete case management workflow."""
        print("\n🧪 Testing case management workflow...")

        # First, create a transaction that will need investigation
        transaction = self.generate_test_transaction(
            account_id=self.test_data["watchlist_account"],
            amount=25.0
        )

        result = self.submit_transaction(transaction)
        if not result:
            self.log_test_result("Case Management Setup", False, "Failed to submit transaction for case")
            return

        transaction_id = transaction['transaction_id']

        # Wait for transaction processing
        if not self.wait_for_transaction_processing(transaction_id):
            self.log_test_result("Case Management Setup", False, "Transaction not processed")
            return

        # Create a case for investigation
        case_payload = {
            "transaction_id": transaction_id,
            "assigned_to": "<EMAIL>",
            "status": "OPEN"
        }

        response = requests.post(f"{self.base_url}/case", json=case_payload)
        if response.status_code == 200:
            self.log_test_result("Case Creation", True, f"Case created for transaction {transaction_id}")

            # Test case status updates
            self._test_case_status_updates(transaction_id)

            # Test case retrieval
            self._test_case_retrieval(transaction_id)

            # Test report attachment
            self._test_report_attachment(transaction_id)

            # Test case closure
            self._test_case_closure(transaction_id)

        else:
            self.log_test_result("Case Creation", False, f"Failed to create case: {response.status_code}")
            pretty_print(response)

    def _test_case_status_updates(self, transaction_id: str):
        """Test case status update functionality."""
        update_payload = {
            "transaction_id": transaction_id,
            "assigned_to": "<EMAIL>",
            "status": "IN_PROGRESS"
        }

        response = requests.put(f"{self.base_url}/case/status", json=update_payload)
        if response.status_code == 200:
            self.log_test_result("Case Status Update", True, "Case status updated to IN_PROGRESS")
        else:
            self.log_test_result("Case Status Update", False, f"Failed to update case status: {response.status_code}")

    def _test_case_retrieval(self, transaction_id: str):
        """Test case retrieval functionality."""
        response = requests.get(f"{self.base_url}/case", params={"transaction_id": transaction_id})
        if response.status_code == 200:
            case_data = response.json().get("data", {})
            if case_data.get("transaction_id") == transaction_id:
                self.log_test_result("Case Retrieval", True, "Case retrieved successfully")
            else:
                self.log_test_result("Case Retrieval", False, "Retrieved case data mismatch")
        else:
            self.log_test_result("Case Retrieval", False, f"Failed to retrieve case: {response.status_code}")

    def _test_report_attachment(self, transaction_id: str):
        """Test report attachment to case."""
        report_payload = {"transaction_id": transaction_id}

        response = requests.post(f"{self.base_url}/report", json=report_payload)
        if response.status_code == 200:
            self.log_test_result("Report Attachment", True, "Report attached to case")
        else:
            self.log_test_result("Report Attachment", False, f"Failed to attach report: {response.status_code}")

    def _test_case_closure(self, transaction_id: str):
        """Test case closure functionality."""
        close_payload = {"transaction_id": transaction_id}

        response = requests.put(f"{self.base_url}/case/close", json=close_payload)
        if response.status_code == 200:
            self.log_test_result("Case Closure", True, "Case closed successfully")
        else:
            self.log_test_result("Case Closure", False, f"Failed to close case: {response.status_code}")

    # ==================== DASHBOARD AND ANALYTICS TESTS ====================

    def test_dashboard_analytics(self):
        """Test dashboard analytics and reporting functionality."""
        print("\n🧪 Testing dashboard analytics...")

        # Test transaction summary
        self._test_transaction_summary()

        # Test evaluated transactions queries
        self._test_evaluated_transactions_queries()

        # Test case listings
        self._test_case_listings()

    def _test_transaction_summary(self):
        """Test transaction summary endpoint."""
        today = datetime.now().strftime("%Y-%m-%d")
        params = {
            "start_date": today,
            "end_date": today
        }

        response = requests.get(f"{self.base_url}/transaction-summary", params=params)
        if response.status_code == 200:
            data = response.json().get("data", {})
            expected_keys = ["blacklist", "watchlist", "stafflist", "limits", "normal"]

            if all(key in data for key in expected_keys):
                self.log_test_result("Transaction Summary", True, "Summary data structure correct")
            else:
                self.log_test_result("Transaction Summary", False, "Missing expected summary categories")
        else:
            self.log_test_result("Transaction Summary", False, f"Failed to get summary: {response.status_code}")

    def _test_evaluated_transactions_queries(self):
        """Test various evaluated transactions query types."""
        today = datetime.now().strftime("%Y-%m-%d")

        # Test different query types
        query_types = ["all", "normal", "affected", "blacklist", "watchlist"]

        for query_type in query_types:
            params = {
                "start_date": today,
                "end_date": today,
                "query_type": query_type
            }

            response = requests.get(f"{self.base_url}/evaluated-transactions", params=params)
            if response.status_code == 200:
                self.log_test_result(f"Evaluated Transactions Query ({query_type})", True, f"Query type {query_type} successful")
            else:
                self.log_test_result(f"Evaluated Transactions Query ({query_type})", False, f"Query failed: {response.status_code}")

    def _test_case_listings(self):
        """Test case listing endpoints."""
        # Test open cases listing
        response = requests.get(f"{self.base_url}/cases/open")
        if response.status_code == 200:
            self.log_test_result("Open Cases Listing", True, "Open cases retrieved successfully")
        else:
            self.log_test_result("Open Cases Listing", False, f"Failed to get open cases: {response.status_code}")

        # Test closed cases listing
        response = requests.get(f"{self.base_url}/cases/closed")
        if response.status_code == 200:
            self.log_test_result("Closed Cases Listing", True, "Closed cases retrieved successfully")
        else:
            self.log_test_result("Closed Cases Listing", False, f"Failed to get closed cases: {response.status_code}")

    # ==================== HELPER METHODS ====================

    def _verify_transaction_evaluation(self, transaction_id: str, expected_evaluation: Dict[str, Any]):
        """Verify that a transaction has the expected evaluation results."""
        today = datetime.now().strftime("%Y-%m-%d")
        params = {
            "start_date": today,
            "end_date": today,
            "query_type": "all"
        }

        try:
            response = requests.get(f"{self.base_url}/evaluated-transactions", params=params)
            if response.status_code == 200:
                data = response.json().get("data", [])
                for txn in data:
                    if txn.get("transaction_id") == transaction_id:
                        evaluation = txn.get("evaluation", {})

                        # Check if expected evaluation keys are present
                        for key, expected_value in expected_evaluation.items():
                            if key in evaluation:
                                actual_value = evaluation[key]
                                if actual_value == expected_value:
                                    self.log_test_result(f"Evaluation Verification ({key})", True, f"Expected {key}={expected_value}, got {actual_value}")
                                else:
                                    self.log_test_result(f"Evaluation Verification ({key})", False, f"Expected {key}={expected_value}, got {actual_value}")
                            else:
                                if expected_evaluation:  # Only fail if we expected something
                                    self.log_test_result(f"Evaluation Verification ({key})", False, f"Expected key {key} not found in evaluation")

                        return

                self.log_test_result("Transaction Verification", False, f"Transaction {transaction_id} not found in evaluated transactions")
            else:
                self.log_test_result("Transaction Verification", False, f"Failed to query evaluated transactions: {response.status_code}")
        except Exception as e:
            self.log_test_result("Transaction Verification", False, f"Error verifying transaction: {e}")

    def test_lists_management(self):
        """Test lists management functionality."""
        print("\n🧪 Testing lists management...")

        # Test creating, reading, updating, and deleting list entries
        test_account = f"ACCT{uuid.uuid4().hex[:8].upper()}"

        # Create a test entry
        payload = {
            "list_type": "WATCHLIST",
            "channel": "WEB",
            "entity_type": "ACCOUNT",
            "account_id": test_account,
            "notes": "Test entry for lists management",
            "created_by": "automated_test"
        }

        response = requests.post(f"{self.base_url}/lists", json=payload)
        if response.status_code == 200:
            self.log_test_result("List Entry Creation", True, f"Created list entry for {test_account}")

            # Test reading the entry
            params = {
                "list_type": "WATCHLIST",
                "channel": "WEB",
                "entity_type": "ACCOUNT",
                "account_id": test_account
            }

            response = requests.get(f"{self.base_url}/lists", params=params)
            if response.status_code == 200:
                self.log_test_result("List Entry Retrieval", True, "Retrieved list entry successfully")
            else:
                self.log_test_result("List Entry Retrieval", False, f"Failed to retrieve entry: {response.status_code}")

            # Test updating the entry
            update_payload = {
                "list_type": "WATCHLIST",
                "channel": "WEB",
                "entity_type": "ACCOUNT",
                "account_id": test_account,
                "notes": "Updated test entry"
            }

            response = requests.put(f"{self.base_url}/lists", json=update_payload)
            if response.status_code == 200:
                self.log_test_result("List Entry Update", True, "Updated list entry successfully")
            else:
                self.log_test_result("List Entry Update", False, f"Failed to update entry: {response.status_code}")

            # Test deleting the entry
            response = requests.delete(f"{self.base_url}/lists", params=params)
            if response.status_code == 200:
                self.log_test_result("List Entry Deletion", True, "Deleted list entry successfully")
            else:
                self.log_test_result("List Entry Deletion", False, f"Failed to delete entry: {response.status_code}")
        else:
            self.log_test_result("List Entry Creation", False, f"Failed to create list entry: {response.status_code}")

    def test_limits_management(self):
        """Test limits management functionality."""
        print("\n🧪 Testing limits management...")

        test_account = f"ACCT{uuid.uuid4().hex[:8].upper()}"

        # Create test limits
        payload = {
            "channel": "WEB",
            "account_id": test_account,
            "AMOUNT": 100,
            "HOURLY_SUM": 500,
            "DAILY_SUM": 2000,
            "WEEKLY_SUM": 10000,
            "MONTHLY_SUM": 40000,
            "HOURLY_COUNT": 10,
            "DAILY_COUNT": 50,
            "WEEKLY_COUNT": 200,
            "MONTHLY_COUNT": 800
        }

        response = requests.post(f"{self.base_url}/limits/account", json=payload)
        if response.status_code == 200:
            self.log_test_result("Limits Creation", True, f"Created limits for {test_account}")

            # Test reading limits
            params = {"channel": "WEB", "account_id": test_account}
            response = requests.get(f"{self.base_url}/limits/account", params=params)
            if response.status_code == 200:
                self.log_test_result("Limits Retrieval", True, "Retrieved limits successfully")
            else:
                self.log_test_result("Limits Retrieval", False, f"Failed to retrieve limits: {response.status_code}")

            # Test updating limits
            update_payload = {
                "channel": "WEB",
                "account_id": test_account,
                "DAILY_SUM": 3000,
                "DAILY_COUNT": 75
            }

            response = requests.put(f"{self.base_url}/limits/account", json=update_payload)
            if response.status_code == 200:
                self.log_test_result("Limits Update", True, "Updated limits successfully")
            else:
                self.log_test_result("Limits Update", False, f"Failed to update limits: {response.status_code}")

            # Test deleting limits
            response = requests.delete(f"{self.base_url}/limits/account", params=params)
            if response.status_code == 200:
                self.log_test_result("Limits Deletion", True, "Deleted limits successfully")
            else:
                self.log_test_result("Limits Deletion", False, f"Failed to delete limits: {response.status_code}")
        else:
            self.log_test_result("Limits Creation", False, f"Failed to create limits: {response.status_code}")

    # ==================== MAIN TEST EXECUTION ====================

    def run_comprehensive_tests(self):
        """Run the complete test suite."""
        print("🚀 Starting Comprehensive FraudPy System Tests")
        print(f"🎯 Target API: {self.base_url}")
        print("=" * 80)

        start_time = time.time()

        try:
            # Setup test data
            self.setup_test_data()

            # Core transaction processing tests
            self.test_normal_transaction_processing()
            self.test_blacklist_transaction_blocking()
            self.test_watchlist_transaction_flagging()
            self.test_staff_transaction_exemption()
            self.test_transaction_limits_enforcement()
            self.test_velocity_limits_enforcement()

            # Case management workflow tests
            self.test_case_management_workflow()

            # Dashboard and analytics tests
            self.test_dashboard_analytics()

            # Administrative functionality tests
            self.test_lists_management()
            self.test_limits_management()

        except Exception as e:
            print(f"❌ Test suite failed with error: {e}")
            self.log_test_result("Test Suite Execution", False, str(e))

        end_time = time.time()
        duration = end_time - start_time

        # Generate test report
        self.generate_test_report(duration)

    def generate_test_report(self, duration: float):
        """Generate a comprehensive test report."""
        print("\n" + "=" * 80)
        print("📊 COMPREHENSIVE TEST REPORT")
        print("=" * 80)

        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests

        print(f"📈 Test Summary:")
        print(f"   Total Tests: {total_tests}")
        print(f"   ✅ Passed: {passed_tests}")
        print(f"   ❌ Failed: {failed_tests}")
        print(f"   ⏱️  Duration: {duration:.2f} seconds")
        print(f"   📊 Success Rate: {(passed_tests/total_tests*100):.1f}%")

        if failed_tests > 0:
            print(f"\n❌ Failed Tests:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"   • {result['test_name']}: {result['details']}")

        print(f"\n📋 Detailed Results:")
        for result in self.test_results:
            status = "✅" if result["success"] else "❌"
            print(f"   {status} {result['test_name']}")
            if result["details"]:
                print(f"      └─ {result['details']}")

        # Save detailed report to file
        self.save_test_report_to_file(duration)

        print("\n🎯 Business Use Case Coverage:")
        print("   ✓ Transaction submission and processing pipeline")
        print("   ✓ Fraud rule evaluation (blacklist, watchlist, stafflist)")
        print("   ✓ Transaction limits enforcement")
        print("   ✓ Case management workflow")
        print("   ✓ Dashboard analytics and reporting")
        print("   ✓ Administrative functions (lists and limits management)")

        print("\n" + "=" * 80)

        if failed_tests == 0:
            print("🎉 ALL TESTS PASSED! The FraudPy system is functioning correctly.")
        else:
            print(f"⚠️  {failed_tests} test(s) failed. Please review the issues above.")

        print("=" * 80)

    def save_test_report_to_file(self, duration: float):
        """Save detailed test report to a file."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"tests/comprehensive_test_report_{timestamp}.json"

        report_data = {
            "test_execution": {
                "timestamp": datetime.now().isoformat(),
                "duration_seconds": duration,
                "base_url": self.base_url
            },
            "summary": {
                "total_tests": len(self.test_results),
                "passed_tests": sum(1 for r in self.test_results if r["success"]),
                "failed_tests": sum(1 for r in self.test_results if not r["success"]),
                "success_rate": sum(1 for r in self.test_results if r["success"]) / len(self.test_results) * 100
            },
            "test_results": self.test_results,
            "test_data_used": self.test_data
        }

        try:
            with open(filename, 'w') as f:
                json.dump(report_data, f, indent=2)
            print(f"📄 Detailed report saved to: {filename}")
        except Exception as e:
            print(f"❌ Failed to save report to file: {e}")


def main():
    """Main entry point for the comprehensive test suite."""
    print("🔍 FraudPy Comprehensive End-to-End Test Suite")
    print("This test suite validates the complete fraud detection system business logic")
    print()

    # Check if BASE_URL is configured
    if not BASE_URL:
        print("❌ Error: BASE_URL not configured. Please create a .env file with your API endpoint.")
        print("Example: BASE_URL=https://your-api-id.execute-api.region.amazonaws.com/Prod/")
        return

    # Initialize and run test suite
    test_suite = FraudSystemTestSuite()
    test_suite.run_comprehensive_tests()


if __name__ == "__main__":
    main()
