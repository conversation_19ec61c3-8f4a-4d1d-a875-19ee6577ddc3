# Evaluated-Transactions Service – Technical Specification

The **Evaluated-Transactions** service is implemented in
`evaluated_transactions/app.py` and deployed as
`EvaluatedTransactionsFunction` in `template.yaml`.  
It exposes **one** GET endpoint – `/evaluated-transactions` – that powers the
dashboard analytics views. The Lambda inspects query-string parameters, builds a
DynamoDB **partition-key** pattern, queries the
`FRAUD_PROCESSED_TRANSACTIONS_TABLE`, post-processes the items and returns a
normalised JSON array.

---

## 1. DynamoDB Schema (`FRAUD_PROCESSED_TRANSACTIONS_TABLE`)

| Attribute         | Type | Notes |
|-------------------|------|-------|
| `PARTITION_KEY`   | S    | Pattern: `EVALUATED-<FILTER>`.<br>Examples:<br>• `EVALUATED` (all transactions)<br>• `EVALUATED-MOBILE-ACCOUNT-ACCT001`<br>• `EVALUATED-BLACKLIST` |
| `SORT_KEY`        | S    | `<unix_ts>_<uuid>` – enables efficient **time-range** queries with `between()`. |
| `processed_transaction` | M (JSON) | The fully enriched transaction (original + evaluation + aggregates). |

No GSIs/LSIs – all reads use **PK/SK**.

---

## 2. Query Parameters

| Name | Required | Example | Meaning |
|------|----------|---------|---------|
| `start_date` | ✅ | `2025-07-01` | Inclusive start of date range *(UTC)*. |
| `end_date`   | ✅ | `2025-07-02` | Inclusive end of date range *(UTC)*. |
| `query_type` | ❌ | `all` *(default)* | Determines the partition-key pattern (see table below). |
| `channel`    | ❌ | `MOBILE` | When `query_type` = `account/application/...` filters results to one channel. |
| `list_type`  | ❌ | `blacklist` | Required when `query_type=entity_list`. |
| `entity_type`| ❌ | `account` | Used with `entity_list` to further narrow scanning logic. |
| `account_id`,`application_id`,`merchant_id`,`product_id` | ❌ | As needed | Supply the identifiers that match the selected hierarchy level. |

### 2.1 `query_type` → partition-key mapping

| query_type | Partition-key generated by `construct_partition_key()` |
|------------|--------------------------------------------------------|
| `all`, `normal`, `affected` | `EVALUATED` |
| `account`                   | `EVALUATED-<channel>-ACCOUNT-<account_id>` |
| `application`               | `EVALUATED-<channel>-APPLICATION-<application_id>` |
| `merchant`                  | `EVALUATED-<channel>-MERCHANT-<application_id>__<merchant_id>` |
| `product`                   | `EVALUATED-<channel>-PRODUCT-<application_id>__<merchant_id>__<product_id>` |
| `blacklist / watchlist / stafflist / limit / card-diff-country-6h` | `EVALUATED-<LIST_TYPE>` *(upper-case)* |
| `entity_list`               | `EVALUATED-<list_type.upper()>` |

---

## 3. Endpoint Specification

### 3.1 GET `/evaluated-transactions` – *General / “all” view*

**Request**

```http
GET /evaluated-transactions?start_date=2025-07-01&end_date=2025-07-02
```

**Successful response – 200**

```json
{
  "responseCode": 200,
  "responseMessage": "Operation Successful",
  "data": [
    {
      "transaction_id": "TXN001",
      "date": "2025-07-01T08:15:23",
      "amount": 120.50,
      "currency": "GHS",
      "country": "GH",
      "channel": "MOBILE",
      "account_id": "ACCT001",
      "application_id": "APP01",
      "merchant_id": "MERCH1",
      "product_id": "PROD9",
      "assigned_to": "",
      "evaluation": {},
      "relevant_aggregates": { "...": "trimmed" }
    }
  ]
}
```

### 3.2 GET `/evaluated-transactions` – *Filtered by **account***  

```http
GET /evaluated-transactions?start_date=2025-07-01&end_date=2025-07-02&query_type=account&channel=MOBILE&account_id=ACCT001
```

Returns only transactions for that account on the specified channel.

**Successful response – 200**

```json
{
  "responseCode": 200,
  "responseMessage": "Operation Successful",
  "data": [
    {
      "transaction_id": "TXN002",
      "date": "2025-07-01T12:05:00",
      "amount": 55.00,
      "currency": "GHS",
      "country": "GH",
      "channel": "MOBILE",
      "account_id": "ACCT001",
      "application_id": "APP01",
      "merchant_id": "MERCH3",
      "product_id": "PROD1",
      "assigned_to": "",
      "evaluation": {},
      "relevant_aggregates": {}
    }
  ]
}
```

### 3.3 GET `/evaluated-transactions` – *Affected vs Normal*

| query_type | Behaviour |
|------------|-----------|
| `normal`   | Lambda filters `evaluation == {}` (no rule triggered). |
| `affected` | Filters `evaluation != {}` (at least one rule triggered). |

**Example – `normal`**

```http
GET /evaluated-transactions?start_date=2025-07-01&end_date=2025-07-02&query_type=normal
```

```json
{
  "responseCode": 200,
  "responseMessage": "Operation Successful",
  "data": [
    { "transaction_id": "TXN003", "evaluation": {} }
  ]
}
```

**Example – `affected`**

```http
GET /evaluated-transactions?start_date=2025-07-01&end_date=2025-07-02&query_type=affected
```

```json
{
  "responseCode": 200,
  "responseMessage": "Operation Successful",
  "data": [
    {
      "transaction_id": "TXN004",
      "evaluation": {
        "amount_exceeded_account": { "rule_version": "1.0" }
      }
    }
  ]
}
```

---

## 4. Processing Logic (inside `app.py`)

1. **Validate** `start_date`/`end_date` (400 on failure).  
2. Convert to Unix epoch & compute `SORT_KEY` range.  
3. **Build partition-key** via `construct_partition_key()`.  
4. Choose query path:  
   * `entity_list` → `query_transactions_by_entity_and_list()`  
   * otherwise       → `query_transactions()`  
5. Each item is *post-processed* to:  
   * Extract `original_transaction` & `evaluation`.  
   * Compute `assigned_to` with `assigned_status()`.  
   * Transform `aggregates` via `transform_aggregates()`.  
6. Return `response(200, {"items": processed_items})`.

---

## 5. Error Handling

| HTTP | Reason |
|------|--------|
| 400  | Missing/invalid `start_date` / `end_date` / query parameters. |
| 404  | *(Not used – endpoint always returns array, possibly empty)* |
| 500  | Unhandled exception (logged to CloudWatch). |

---

## 6. IAM Permissions

The Lambda role referenced in `template.yaml` already attaches **DynamoDBCrudPolicy**
for **all tables** (`TableName: '*'`). For least-privilege environments restrict
to the ARN of `FRAUD_PROCESSED_TRANSACTIONS_TABLE`.

---

## 7. Open Items / TODO

* Implement pagination (`limit` + `last_evaluated_key`) for large result sets.  
* Replace `print()` statements with structured logging (AWS X-Ray IDs).  
* Unit tests for each helper (`parse_key`, `transform_aggregates`, …).  
* Consider DDB **GSI** on `channel` to optimise filtered scans.
