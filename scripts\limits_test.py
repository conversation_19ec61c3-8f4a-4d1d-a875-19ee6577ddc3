import requests
from common import BASE_URL, pretty_print

def test_get_limits():
    print("Testing GET /limits/account")
    params = {"channel": "MOBILE", "account_id": "ACCT001"}
    resp = requests.get(f"{BASE_URL}/limits/account", params=params)
    pretty_print(resp)

def main():
    print("Running Limits API tests against:", BASE_URL)
    test_get_limits()

def test_create_limit():
    # New Test Case:
    # Create a new limit for an account using POST.
    # Expected: API returns 200 OK and a confirmation message.
    print("Testing create limit")
    payload = {
        "channel": "MOBILE",
        "account_id": "ACCT002",
        "AMOUNT": 2000,
        "HOURLY_SUM": 10000,
        "DAILY_SUM": 30000,
        "WEEKLY_SUM": 100000,
        "MONTHLY_SUM": 200000,
        "HOURLY_COUNT": 50,
        "DAILY_COUNT": 500,
        "WEEKLY_COUNT": 2000,
        "MONTHLY_COUNT": 8000
    }
    resp = requests.post(f"{BASE_URL}/limits/account", json=payload)
    pretty_print(resp)

def test_update_limit():
    # New Test Case:
    # Update an existing limit for an account using PUT.
    # Expected: API returns 200 OK and a confirmation update message.
    print("Testing update limit")
    payload = {
        "channel": "MOBILE",
        "account_id": "ACCT002",
        "DAILY_SUM": 30000,
        "DAILY_COUNT": 450
    }
    resp = requests.put(f"{BASE_URL}/limits/account", json=payload)
    pretty_print(resp)

def test_delete_limit():
    # New Test Case:
    # Delete an existing limit for an account using DELETE.
    # Expected: API returns 200 OK with deletion confirmation.
    print("Testing delete limit")
    params = {"channel": "MOBILE", "account_id": "ACCT002"}
    resp = requests.delete(f"{BASE_URL}/limits/account", params=params)
    pretty_print(resp)

def test_get_limit_invalid_params():
    # New Test Case:
    # Attempt to retrieve a limit with missing or invalid query parameters.
    # Expected: The API should return an error (e.g., 400).
    print("Testing get limits with invalid parameters")
    params = {"channel": "MOBILE"}  # Missing account_id
    resp = requests.get(f"{BASE_URL}/limits/account", params=params)
    pretty_print(resp)

def test_get_account_application_limit():
    # New Test Case:
    # Retrieve account application limit using GET on /limits/account-application.
    # Expected: 200 OK and a response with limit details, or 404 if no limit exists.
    print("Testing GET /limits/account-application")
    params = {"channel": "MOBILE", "account_id": "ACCT003", "application_id": "APP001"}
    resp = requests.get(f"{BASE_URL}/limits/account-application", params=params)
    pretty_print(resp)

if __name__ == "__main__":
    main()
    test_create_limit()
    test_update_limit()
    test_delete_limit()
    test_get_limit_invalid_params()
    test_get_account_application_limit()
