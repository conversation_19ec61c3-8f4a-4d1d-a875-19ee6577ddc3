# fraud-py-v1-transaction-router-lambda – Technical Specification  

## 1. Overview  
Serves as the **fan-out / partitioning** stage immediately after ingress.  
Its role is deceptively simple but extremely important for horizontal
scalability and hot-partition avoidance inside the pipeline.

Detailed behaviour:

1. **Shard Selection Strategy** – today the code pins `shard = 1` for predictable
   demos.  A commented line shows how to switch to `random.randint(1, 20)`.
   Production deployments are expected to replace this with **consistent
   hashing** (e.g., on `account_id`) so that all transactions for the same
   entity always arrive on the same statistical-calculator shard.  
2. **Environment-driven Topology** – for every potential shard the SAM template
   exports an env-var
   `FRAUD_PY_V1_RAW_TRANSACTIONS_QUEUE_URL_SHARD_<n>`.  The Lambda performs a
   lookup and fails fast with a clear `ValueError` if the operator forgot to
   wire a queue URL, preventing silent message loss.  
3. **Pure Router – No Mutation** – the message body is forwarded as-is; FIFO
   ordering and content-based deduplication remain intact because the router
   does **not** add new attributes.  This preserves the original
   `MessageDeduplicationId` generated by the Mock-Producer to guarantee
   idempotence across the entire fan-out boundary.  
4. **Operational Safety Nets** – because the Lambda is SQS-triggered, any
   unhandled exception (e.g., missing env var) automatically causes the batch
   to be retried and ultimately end up in the queue’s DLQ, ensuring no data
   loss during misconfiguration events.  
5. **Zero Business Logic** – by design the router must never inspect domain
   fields; all fraud rules belong further down the chain.  This makes it safe
   to horizontally scale or even rewrite in a different language without
   impacting fraud correctness.

Therefore the Transaction-Router Lambda is the glue that ensures Elastic
scaling is possible without coupling later Lambdas to the physical queue
topology.

## 2. Runtime Contract  
| Item | Value |
| ---- | ----- |
| **Handler** | `fraud-py-v1-transaction-router-lambda.app.lambda_handler` |
| **Trigger** | SQS → `FraudPyV1IncomingTransactionsQueue.fifo` |
| **Output** | SQS → `FraudPyV1RawTransactionsQueueShard1.fifo` |

### Environment Variables  
`FRAUD_PY_V1_RAW_TRANSACTIONS_QUEUE_URL_SHARD_1` – SQS URL of shard 1  
(`..._SHARD_{n}` placeholders reserved).

## 3. Core Logic Flow  
1. **Shard Selection** – currently fixed `shard = 1`; commented line shows future `random.randint(1,20)`.  
2. **Queue URL Resolution** – builds env var name, fetches URL; raises `ValueError`
   if unset → message batch will be retried then DLQ’d by SQS.  
3. **Forwarding Loop** – for each record in `event['Records']` it re-publishes:  
   * `MessageGroupId="FraudPyV1TransactionRouterLambdaGroup"`  
   * `MessageDeduplicationId` not re-set (SQS content-based dedupe active).  
4. **No Return** – success/failure handled by Lambda/SQS integration.

## 4. Error Handling & Observability  
* Single missing env var aborts invocation.  
* Prints each record body for basic tracing.  
* No CloudWatch custom metrics.

## 5. Future Enhancements  
* Replace hard-coded shard with consistent hashing of `account_id`.  
* Add structured logging & custom CloudWatch metric for routing latency.
