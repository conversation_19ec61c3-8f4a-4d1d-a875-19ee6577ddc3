# fraud-py-v1-mock-producer-lambda – Technical Specification  

## 1. Overview  
Acts as the **single entry-point** into the FraudPy pipeline.  
The module’s responsibilities go far beyond merely “put JSON on SQS”; it  
implements a thin façade that shields external callers from every internal
design decision of the downstream system:

- **Transport Gateway** – behind an API Gateway *HTTP API* `POST /submit`, the
  Lambda handles CORS, status codes and consistent error payloads so that web,
  mobile and server-to-server clients all integrate the same way.  
- **Rigorous Validation** – `validate_transaction()` enforces required keys,
  type hints, ISO-8601 date sanity, enumerated currencies/countries/channels
  *and* guards against empty-string edge-cases.  It purposefully returns a
  **list of all errors** rather than fail-fast so callers can fix everything in
  one round-trip.  
- **Schema Translation Layer** – external field names (`processor`,
  `account_ref`, …) are remapped to the internal canonical schema used by all
  downstream Lambdas.  The translation is fully reversible to guarantee lossless
  round-trips in synchronous mode.  
- **Idempotent Ingestion** – a deterministic internal id  
  `"{channel}__{application_id}__{transaction_id}"` is generated to serve as
  both the DynamoDB *SORT_KEY* and SQS `MessageDeduplicationId`, guaranteeing
  exactly-once semantics even if clients retry.  
- **Asynchronous Kick-off** – the validated payload is published to  
  `FraudPyV1IncomingTransactionsQueue.fifo` with `MessageGroupId` fixed so the
  router receives transactions in order per Lambda container.  
- **Optional Synchronous Response** – if the caller prefers a blocking call the
  Lambda will poll `FraudPyV1ProcessedTransactionsTable` for up to
  **12 seconds** (configurable via env-vars) and return a terse evaluation
  summary plus `processing_time`.  This pattern supports both fire-and-forget
  integrations **and** classic request/response flows.  
- **Developer-friendly Responses** – `create_api_response()` unifies CORS,
  JSON structure and error surfacing; front-end engineers never need to parse
  raw AWS Lambda errors.

In short, the Mock-Producer Lambda converts an untrusted HTTP request into a
well-formed, deduplicated, internally-schema’d message that the rest of the
fraud pipeline can consume with zero further validation.

## 2. Runtime Contract  
| Item | Value |
| ---- | ----- |
| **Handler** | `fraud-py-v1-mock-producer-lambda.app.lambda_handler` |
| **Trigger** | API Gateway `POST /submit` |
| **Output** | SQS → `FraudPyV1IncomingTransactionsQueue.fifo` |
| **Polling** | DynamoDB → `FraudPyV1ProcessedTransactionsTable` |

### Environment Variables  
| Name | Description |
|------|-------------|
| `FRAUD_PY_V1_INCOMING_TRANSACTIONS_QUEUE_URL` | Target SQS queue URL |
| `FRAUD_PY_V1_PROCESSED_TRANSACTIONS_TABLE_NAME` | Table for lookup while polling |
| `POLLING_TIMEOUT_SECONDS` | (hard-coded = 12 s) |
| `POLLING_INTERVAL_MS` | (hard-coded = 50 ms) |

## 3. Core Logic Flow  
1. **Parse / Validate** – `validate_transaction()` checks required fields, types, ISO-8601 date, enumerations.  
2. **Translate Keys** – `translate_transaction_keys(direction='inbound')` converts `processor➜application_id`, `account_ref➜account_id`.  
3. **Generate Internal ID** – `generate_internal_transaction_id()` creates:  
   `"{channel}__{application_id}__{transaction_id}"`.  
4. **De-duplication Check** – `retrieve_evaluation()` point-reads DynamoDB to short-circuit repeat submissions.  
5. **Enqueue** – `send_to_sqs()` publishes the JSON body to SQS using:  
   * `MessageGroupId="FraudPyV1MockProducerGroup"`  
   * `MessageDeduplicationId=<client transaction_id>`  
6. **Synchronous Poll** – loops (`≤ 12 s`) calling `retrieve_evaluation()` every 50 ms; returns 200 on success, 408 on timeout.  
7. **API Response** – `create_api_response()` wraps payload, sets CORS headers.

## 4. Key Helper Functions  
| Function | Purpose |
|----------|---------|
| `validate_transaction()` | Schema validation & business rules |
| `translate_transaction_keys()` | Bidirectional key translation |
| `generate_internal_transaction_id()` | Compose unique pipeline id |
| `send_to_sqs()` | Fire-and-forget publisher |
| `retrieve_evaluation()` | ConsistentRead point-lookup |
| `create_summary_response()` | Build client-friendly view |

## 5. Error Handling & Metrics  
* Returns **400** for validation/JSON decode errors, **408** for poll timeout, **500** for unexpected exceptions.  
* No CloudWatch custom metrics; latency is returned to caller as `processing_time`.
