"""
Smoke-tests for the **Evaluated-Transactions** endpoint.

Run:

    python scripts\\evaluated_transactions_test.py
"""
import requests
from common import BASE_URL, pretty_print


def get_evaluated_transactions(
    start_date: str,
    end_date: str,
    query_type: str = "all",
    channel: str = "",
    extra_params: dict = None,
) -> None:
    # Detailed Test Case:
    # Send a GET request to /evaluated-transactions with specified parameters.
    # Expected: 200 OK with a JSON response containing an array of evaluated transactions,
    # which may be empty if no data matches the criteria.
    params = {
        "start_date": start_date,
        "end_date": end_date,
        "query_type": query_type,
        "channel": channel,
    }
    if extra_params:
        params.update(extra_params)
    pretty_print(requests.get(f"{BASE_URL}/evaluated-transactions", params=params))


def main() -> None:
    print("Running Evaluated-Transactions API smoke tests against:", BASE_URL)

    # Adjust dates / parameters to match data in your DynamoDB table
    print("Test: All evaluated transactions")
    get_evaluated_transactions("2024-01-01", "2024-01-31")
    print("Test: Normal evaluated transactions")
    get_evaluated_transactions("2024-01-01", "2024-01-31", query_type="normal")
    print("Test: Affected evaluated transactions")
    get_evaluated_transactions("2024-01-01", "2024-01-31", query_type="affected")
    print("Test: Account evaluated transactions")
    # Simulate an account-based query by passing an extra parameter for account_id.
    get_evaluated_transactions("2024-01-01", "2024-01-31", query_type="account", channel="MOBILE", extra_params={"account_id": "ACCT001"})


def test_get_evaluated_transactions_invalid_dates():
    # New Test Case:
    # Attempt to fetch evaluated transactions using an invalid date range (start_date > end_date).
    # Expected: The API should respond with a 502 error.
    print("Testing evaluated transactions with invalid date range - expecting 502 error")
    params = {
        "start_date": "2025-08-01",
        "end_date": "2025-07-01",
        "query_type": "all",
        "channel": ""
    }
    resp = requests.get(f"{BASE_URL}/evaluated-transactions", params=params)
    if resp.status_code == 502:
        print("Received expected 502 error for invalid date range.")
    else:
        print("Unexpected status code for invalid date range:", resp.status_code)
    pretty_print(resp)

if __name__ == "__main__":
    main()
    test_get_evaluated_transactions_invalid_dates()
