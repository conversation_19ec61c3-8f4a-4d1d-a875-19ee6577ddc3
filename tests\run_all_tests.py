#!/usr/bin/env python3
"""
Test Runner for FraudPy System

This script provides options to run different test suites:
1. Comprehensive end-to-end tests (recommended)
2. Individual component tests
3. Quick smoke tests

Usage:
    python tests/run_all_tests.py [--mode=comprehensive|individual|smoke]
"""

import argparse
import subprocess
import sys
import time
from pathlib import Path
from datetime import datetime


def run_comprehensive_tests():
    """Run the comprehensive end-to-end test suite."""
    print("🚀 Running Comprehensive End-to-End Test Suite")
    print("=" * 60)
    
    try:
        result = subprocess.run([
            sys.executable, 
            "tests/comprehensive_fraud_system_test.py"
        ], check=True, capture_output=False)
        
        return result.returncode == 0
    except subprocess.CalledProcessError as e:
        print(f"❌ Comprehensive tests failed with exit code: {e.returncode}")
        return False
    except Exception as e:
        print(f"❌ Error running comprehensive tests: {e}")
        return False


def run_individual_tests():
    """Run individual component test scripts."""
    print("🔧 Running Individual Component Tests")
    print("=" * 60)
    
    test_scripts = [
        "scripts/case_management_test.py",
        "scripts/limits_test.py", 
        "scripts/lists_test.py",
        "scripts/evaluated_transactions_test.py",
        "scripts/transaction_summary_test.py"
    ]
    
    results = {}
    
    for script in test_scripts:
        script_path = Path(script)
        if not script_path.exists():
            print(f"⚠️  Skipping {script} - file not found")
            continue
            
        print(f"\n🧪 Running {script_path.name}...")
        try:
            result = subprocess.run([
                sys.executable, str(script_path)
            ], check=True, capture_output=True, text=True)
            
            results[script] = True
            print(f"✅ {script_path.name} completed successfully")
            
        except subprocess.CalledProcessError as e:
            results[script] = False
            print(f"❌ {script_path.name} failed with exit code: {e.returncode}")
            if e.stdout:
                print("STDOUT:", e.stdout[-500:])  # Last 500 chars
            if e.stderr:
                print("STDERR:", e.stderr[-500:])  # Last 500 chars
        except Exception as e:
            results[script] = False
            print(f"❌ Error running {script_path.name}: {e}")
    
    # Summary
    total_tests = len(results)
    passed_tests = sum(1 for success in results.values() if success)
    
    print(f"\n📊 Individual Tests Summary:")
    print(f"   Total: {total_tests}")
    print(f"   ✅ Passed: {passed_tests}")
    print(f"   ❌ Failed: {total_tests - passed_tests}")
    
    return passed_tests == total_tests


def run_smoke_tests():
    """Run quick smoke tests to verify basic functionality."""
    print("💨 Running Quick Smoke Tests")
    print("=" * 60)
    
    # Import here to avoid dependency issues
    try:
        sys.path.append("scripts")
        from common import BASE_URL
        import requests
        
        if not BASE_URL:
            print("❌ BASE_URL not configured. Please check your .env file.")
            return False
        
        print(f"🎯 Testing API endpoint: {BASE_URL}")
        
        # Test basic endpoints
        smoke_tests = [
            ("Transaction Summary", f"{BASE_URL}/transaction-summary?start_date=2025-01-01&end_date=2025-01-01"),
            ("Open Cases", f"{BASE_URL}/cases/open"),
            ("Closed Cases", f"{BASE_URL}/cases/closed"),
            ("Evaluated Transactions", f"{BASE_URL}/evaluated-transactions?start_date=2025-01-01&end_date=2025-01-01")
        ]
        
        results = {}
        
        for test_name, url in smoke_tests:
            try:
                print(f"🧪 Testing {test_name}...")
                response = requests.get(url, timeout=10)
                
                if response.status_code == 200:
                    results[test_name] = True
                    print(f"✅ {test_name}: OK")
                else:
                    results[test_name] = False
                    print(f"❌ {test_name}: HTTP {response.status_code}")
                    
            except requests.exceptions.Timeout:
                results[test_name] = False
                print(f"❌ {test_name}: Timeout")
            except Exception as e:
                results[test_name] = False
                print(f"❌ {test_name}: {e}")
        
        # Summary
        total_tests = len(results)
        passed_tests = sum(1 for success in results.values() if success)
        
        print(f"\n📊 Smoke Tests Summary:")
        print(f"   Total: {total_tests}")
        print(f"   ✅ Passed: {passed_tests}")
        print(f"   ❌ Failed: {total_tests - passed_tests}")
        
        return passed_tests == total_tests
        
    except ImportError as e:
        print(f"❌ Failed to import required modules: {e}")
        return False


def main():
    """Main entry point for the test runner."""
    parser = argparse.ArgumentParser(
        description="FraudPy Test Runner",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Test Modes:
  comprehensive  - Run complete end-to-end business logic tests (recommended)
  individual     - Run individual component tests separately  
  smoke          - Run quick smoke tests to verify basic API connectivity

Examples:
  python tests/run_all_tests.py --mode=comprehensive
  python tests/run_all_tests.py --mode=smoke
  python tests/run_all_tests.py  # defaults to comprehensive
        """
    )
    
    parser.add_argument(
        "--mode",
        choices=["comprehensive", "individual", "smoke"],
        default="comprehensive",
        help="Test mode to run (default: comprehensive)"
    )
    
    args = parser.parse_args()
    
    print("🔍 FraudPy Test Runner")
    print(f"📅 Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🎯 Mode: {args.mode}")
    print()
    
    start_time = time.time()
    
    # Run selected test mode
    if args.mode == "comprehensive":
        success = run_comprehensive_tests()
    elif args.mode == "individual":
        success = run_individual_tests()
    elif args.mode == "smoke":
        success = run_smoke_tests()
    else:
        print(f"❌ Unknown test mode: {args.mode}")
        return 1
    
    end_time = time.time()
    duration = end_time - start_time
    
    print(f"\n⏱️  Total execution time: {duration:.2f} seconds")
    
    if success:
        print("🎉 All tests completed successfully!")
        return 0
    else:
        print("❌ Some tests failed. Please review the output above.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
