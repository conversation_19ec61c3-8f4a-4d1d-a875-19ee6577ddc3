{"test_execution": {"timestamp": "2025-07-07T08:21:11.691668", "duration_seconds": 28.784066677093506, "base_url": "https://0l6g24j0lk.execute-api.eu-west-1.amazonaws.com/Prod"}, "summary": {"total_tests": 20, "passed_tests": 10, "failed_tests": 10, "success_rate": 50.0}, "test_results": [{"test_name": "Normal Transaction Submission", "success": false, "details": "Failed to submit transaction", "timestamp": "2025-07-07T08:20:49.429478"}, {"test_name": "Blacklist Transaction Submission", "success": false, "details": "Failed to submit transaction", "timestamp": "2025-07-07T08:20:49.985477"}, {"test_name": "Watchlist Transaction Submission", "success": false, "details": "Failed to submit transaction", "timestamp": "2025-07-07T08:20:50.753099"}, {"test_name": "Staff Transaction Submission", "success": false, "details": "Failed to submit transaction", "timestamp": "2025-07-07T08:20:51.536026"}, {"test_name": "Limit Exceeded Transaction Submission", "success": false, "details": "Failed to submit transaction", "timestamp": "2025-07-07T08:20:52.079278"}, {"test_name": "Multiple Transaction Submission", "success": false, "details": "Failed to submit velocity test transactions", "timestamp": "2025-07-07T08:20:54.458961"}, {"test_name": "Case Management Setup", "success": false, "details": "Failed to submit transaction for case", "timestamp": "2025-07-07T08:20:55.027586"}, {"test_name": "Transaction Summary", "success": true, "details": "Summary data structure correct", "timestamp": "2025-07-07T08:20:56.529141"}, {"test_name": "Evaluated Transactions Query (all)", "success": true, "details": "Query type all successful", "timestamp": "2025-07-07T08:20:58.363122"}, {"test_name": "Evaluated Transactions Query (normal)", "success": true, "details": "Query type normal successful", "timestamp": "2025-07-07T08:20:59.198614"}, {"test_name": "Evaluated Transactions Query (affected)", "success": true, "details": "Query type affected successful", "timestamp": "2025-07-07T08:20:59.980522"}, {"test_name": "Evaluated Transactions Query (blacklist)", "success": true, "details": "Query type blacklist successful", "timestamp": "2025-07-07T08:21:00.557952"}, {"test_name": "Evaluated Transactions Query (watchlist)", "success": true, "details": "Query type watchlist successful", "timestamp": "2025-07-07T08:21:01.377403"}, {"test_name": "Open Cases Listing", "success": true, "details": "Open cases retrieved successfully", "timestamp": "2025-07-07T08:21:03.468431"}, {"test_name": "Closed Cases Listing", "success": true, "details": "Closed cases retrieved successfully", "timestamp": "2025-07-07T08:21:04.294804"}, {"test_name": "List Entry Creation", "success": true, "details": "Created list entry for ACCT73C0BA57", "timestamp": "2025-07-07T08:21:05.194489"}, {"test_name": "List Entry Retrieval", "success": true, "details": "Retrieved list entry successfully", "timestamp": "2025-07-07T08:21:07.119257"}, {"test_name": "List Entry Update", "success": false, "details": "Failed to update entry: 400", "timestamp": "2025-07-07T08:21:08.945820"}, {"test_name": "List Entry Deletion", "success": false, "details": "Failed to delete entry: 502", "timestamp": "2025-07-07T08:21:10.876738"}, {"test_name": "Limits Creation", "success": false, "details": "Failed to create limits: 400", "timestamp": "2025-07-07T08:21:11.672455"}], "test_data_used": {"normal_account": "ACCTD2219C53", "blacklist_account": "ACCTAD085970", "watchlist_account": "ACCTA966F7E8", "staff_account": "ACCT1CE0FCDF", "limit_test_account": "ACCT5D585A8E", "velocity_test_account": "ACCT5E8EED0F"}}