"""
Test Configuration for FraudPy System Tests

This module contains configuration settings and test data templates
that can be customized for different testing environments.
"""

import os
from typing import Dict, Any

# Test timeouts and retry settings
TEST_TIMEOUT_SECONDS = int(os.getenv("TEST_TIMEOUT_SECONDS", "30"))
TEST_RETRY_ATTEMPTS = int(os.getenv("TEST_RETRY_ATTEMPTS", "3"))
TRANSACTION_PROCESSING_TIMEOUT = int(os.getenv("TRANSACTION_PROCESSING_TIMEOUT", "30"))
POLLING_INTERVAL_SECONDS = int(os.getenv("POLLING_INTERVAL_SECONDS", "2"))

# Test data templates
DEFAULT_TEST_LIMITS = {
    "AMOUNT": 50,
    "HOURLY_SUM": 200,
    "DAILY_SUM": 1000,
    "WEEKLY_SUM": 5000,
    "MONTHLY_SUM": 20000,
    "HOURLY_COUNT": 5,
    "DAILY_COUNT": 20,
    "WEEKLY_COUNT": 100,
    "MONTHLY_COUNT": 400
}

DEFAULT_TRANSACTION_TEMPLATE = {
    "amount": 25.0,
    "currency": "GHS",
    "country": "GH",
    "channel": "MOBILE",
    "application_id": "APP001",
    "merchant_id": "MERCH001",
    "product_id": "PROD001"
}

# Test scenarios configuration
TEST_SCENARIOS = {
    "normal_transaction": {
        "description": "Normal transaction that should pass all checks",
        "amount": 25.0,
        "expected_evaluation": {}
    },
    "amount_limit_exceeded": {
        "description": "Transaction exceeding single amount limit",
        "amount": 75.0,
        "expected_evaluation": {"amount_exceeded": True}
    },
    "velocity_test": {
        "description": "Multiple transactions to test velocity limits",
        "transaction_count": 3,
        "amount_per_transaction": 10.0,
        "interval_seconds": 1
    }
}

# List types and entity types for testing
LIST_TYPES = ["BLACKLIST", "WATCHLIST", "STAFFLIST"]
ENTITY_TYPES = ["ACCOUNT", "APPLICATION", "MERCHANT", "PRODUCT"]
CHANNELS = ["MOBILE", "WEB", "POS"]

# Case management test data
CASE_STATUSES = ["OPEN", "IN_PROGRESS", "CLOSED"]
TEST_INVESTIGATORS = [
    "<EMAIL>",
    "<EMAIL>", 
    "<EMAIL>"
]

def get_test_config() -> Dict[str, Any]:
    """Get the complete test configuration."""
    return {
        "timeouts": {
            "test_timeout": TEST_TIMEOUT_SECONDS,
            "transaction_processing": TRANSACTION_PROCESSING_TIMEOUT,
            "polling_interval": POLLING_INTERVAL_SECONDS
        },
        "retry": {
            "attempts": TEST_RETRY_ATTEMPTS
        },
        "defaults": {
            "limits": DEFAULT_TEST_LIMITS,
            "transaction": DEFAULT_TRANSACTION_TEMPLATE
        },
        "scenarios": TEST_SCENARIOS,
        "test_data": {
            "list_types": LIST_TYPES,
            "entity_types": ENTITY_TYPES,
            "channels": CHANNELS,
            "case_statuses": CASE_STATUSES,
            "investigators": TEST_INVESTIGATORS
        }
    }
